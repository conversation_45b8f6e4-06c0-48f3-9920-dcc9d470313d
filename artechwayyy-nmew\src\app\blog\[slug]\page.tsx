import { notFound } from "next/navigation";
import { posts } from "@/lib/posts";
import { Header } from "@/components/Header";
import { Footer } from "@/components/Footer";
import Image from "next/image";
import { Badge } from "@/components/ui/badge";

export default function BlogPostPage({
  params,
}: {
  params: { slug: string };
}) {
  const post = posts.find((p) => p.slug === params.slug);

  if (!post) {
    notFound();
  }

  return (
    <div className="flex flex-col min-h-screen">
      <Header />
      <main className="flex-1 py-12 md:py-20">
        <article className="container max-w-4xl mx-auto">
          <header className="mb-8">
            <h1 className="text-4xl md:text-5xl font-bold font-headline leading-tight mb-4">
              {post.title}
            </h1>
            <div className="text-muted-foreground text-sm">
              <span>Published on {post.date}</span>
              <span className="mx-2">•</span>
              <span>{post.category}</span>
            </div>
          </header>

          <div className="relative aspect-video mb-8 rounded-lg overflow-hidden">
            <Image
              src={post.imageUrl}
              alt={post.title}
              fill
              className="object-cover"
              data-ai-hint={post.imageHint}
              priority
            />
          </div>

          <div className="prose prose-lg max-w-none mx-auto text-foreground/80">
            {post.content.split("\\n").map((paragraph, index) => (
              <p key={index}>{paragraph}</p>
            ))}
          </div>

          <footer className="mt-12">
            <div className="flex flex-wrap gap-2">
              {post.tags.split(",").map((tag) => (
                <Badge key={tag} variant="secondary">
                  {tag.trim()}
                </Badge>
              ))}
            </div>
          </footer>
        </article>
      </main>
      <Footer />
    </div>
  );
}

// Add a basic prose style to globals.css if it doesn't exist
// You can enhance this further.
/*
@layer utilities {
  .prose {
    @apply text-lg;
  }
  .prose h1 {
    @apply text-4xl font-bold mb-4;
  }
  .prose h2 {
    @apply text-3xl font-bold mb-4;
  }
  .prose p {
    @apply mb-4 leading-relaxed;
  }
  .prose a {
    @apply text-primary hover:underline;
  }
}
*/
