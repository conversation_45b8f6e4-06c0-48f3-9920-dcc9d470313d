import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

export function NewsletterSignup() {
  return (
    <section className="py-16 md:py-24 bg-muted/50">
      <div className="container">
        <Card className="max-w-2xl mx-auto text-center shadow-lg border-primary/20">
          <CardHeader>
            <CardTitle className="text-3xl font-headline">
              Stay Ahead of the Curve
            </CardTitle>
            <CardDescription>
              Subscribe to our newsletter for the latest in AI, art, and
              technology.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form className="flex w-full items-center space-x-2">
              <Input
                type="email"
                placeholder="Enter your email"
                className="flex-1"
              />
              <Button type="submit">Subscribe</Button>
            </form>
          </CardContent>
        </Card>
      </div>
    </section>
  );
}
