import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTit<PERSON> } from "@/components/ui/card";
import type { LucideIcon } from "lucide-react";
import { cn } from "@/lib/utils";

interface CategoryCardProps {
  title: string;
  icon: LucideIcon;
  className?: string;
}

export function CategoryCard({
  title,
  icon: Icon,
  className,
}: CategoryCardProps) {
  return (
    <Card
      className={cn(
        "text-center transition-all duration-300 hover:scale-105 hover:shadow-lg hover:-translate-y-1 cursor-pointer",
        className
      )}
    >
      <CardHeader>
        <div className="mx-auto bg-primary/10 rounded-full p-3 w-fit">
          <Icon className="h-8 w-8 text-primary" />
        </div>
      </CardHeader>
      <CardContent>
        <CardTitle className="text-lg font-headline">{title}</CardTitle>
      </CardContent>
    </Card>
  );
}
