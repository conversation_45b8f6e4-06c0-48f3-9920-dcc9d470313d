'use server';

/**
 * @fileOverview Content curation flow for categorizing blog posts based on AI analysis.
 *
 * - curateContent - A function that curates and categorizes blog posts.
 * - CurateContentInput - The input type for the curateContent function.
 * - CurateContentOutput - The return type for the curateContent function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const CurateContentInputSchema = z.object({
  title: z.string().describe('The title of the blog post.'),
  content: z.string().describe('The main content of the blog post.'),
});
export type CurateContentInput = z.infer<typeof CurateContentInputSchema>;

const CurateContentOutputSchema = z.object({
  category: z.string().describe('The category the blog post belongs to.'),
  summary: z.string().describe('A short summary of the blog post.'),
});
export type CurateContentOutput = z.infer<typeof CurateContentOutputSchema>;

export async function curateContent(input: CurateContentInput): Promise<CurateContentOutput> {
  return curateContentFlow(input);
}

const prompt = ai.definePrompt({
  name: 'curateContentPrompt',
  input: {schema: CurateContentInputSchema},
  output: {schema: CurateContentOutputSchema},
  prompt: `You are an AI blog content curator. Analyze the following blog post and determine the most relevant category and provide a short summary.

Blog Post Title: {{{title}}}
Blog Post Content: {{{content}}}

Categories: AI News, AI Design, AI for Business, AI Marketing, Future of AI.

Respond with the category and summary in the format specified by the CurateContentOutputSchema schema.`,
});

const curateContentFlow = ai.defineFlow(
  {
    name: 'curateContentFlow',
    inputSchema: CurateContentInputSchema,
    outputSchema: CurateContentOutputSchema,
  },
  async input => {
    const {output} = await prompt(input);
    return output!;
  }
);
