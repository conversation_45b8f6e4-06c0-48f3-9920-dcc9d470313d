{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/artechway/artechwayyy-nmew/src/app/globals.css"], "sourcesContent": ["*, ::before, ::after {\n  --tw-border-spacing-x: 0;\n  --tw-border-spacing-y: 0;\n  --tw-translate-x: 0;\n  --tw-translate-y: 0;\n  --tw-rotate: 0;\n  --tw-skew-x: 0;\n  --tw-skew-y: 0;\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  --tw-pan-x:  ;\n  --tw-pan-y:  ;\n  --tw-pinch-zoom:  ;\n  --tw-scroll-snap-strictness: proximity;\n  --tw-gradient-from-position:  ;\n  --tw-gradient-via-position:  ;\n  --tw-gradient-to-position:  ;\n  --tw-ordinal:  ;\n  --tw-slashed-zero:  ;\n  --tw-numeric-figure:  ;\n  --tw-numeric-spacing:  ;\n  --tw-numeric-fraction:  ;\n  --tw-ring-inset:  ;\n  --tw-ring-offset-width: 0px;\n  --tw-ring-offset-color: #fff;\n  --tw-ring-color: rgb(59 130 246 / 0.5);\n  --tw-ring-offset-shadow: 0 0 #0000;\n  --tw-ring-shadow: 0 0 #0000;\n  --tw-shadow: 0 0 #0000;\n  --tw-shadow-colored: 0 0 #0000;\n  --tw-blur:  ;\n  --tw-brightness:  ;\n  --tw-contrast:  ;\n  --tw-grayscale:  ;\n  --tw-hue-rotate:  ;\n  --tw-invert:  ;\n  --tw-saturate:  ;\n  --tw-sepia:  ;\n  --tw-drop-shadow:  ;\n  --tw-backdrop-blur:  ;\n  --tw-backdrop-brightness:  ;\n  --tw-backdrop-contrast:  ;\n  --tw-backdrop-grayscale:  ;\n  --tw-backdrop-hue-rotate:  ;\n  --tw-backdrop-invert:  ;\n  --tw-backdrop-opacity:  ;\n  --tw-backdrop-saturate:  ;\n  --tw-backdrop-sepia:  ;\n  --tw-contain-size:  ;\n  --tw-contain-layout:  ;\n  --tw-contain-paint:  ;\n  --tw-contain-style:  ;\n}\n\n::backdrop {\n  --tw-border-spacing-x: 0;\n  --tw-border-spacing-y: 0;\n  --tw-translate-x: 0;\n  --tw-translate-y: 0;\n  --tw-rotate: 0;\n  --tw-skew-x: 0;\n  --tw-skew-y: 0;\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  --tw-pan-x:  ;\n  --tw-pan-y:  ;\n  --tw-pinch-zoom:  ;\n  --tw-scroll-snap-strictness: proximity;\n  --tw-gradient-from-position:  ;\n  --tw-gradient-via-position:  ;\n  --tw-gradient-to-position:  ;\n  --tw-ordinal:  ;\n  --tw-slashed-zero:  ;\n  --tw-numeric-figure:  ;\n  --tw-numeric-spacing:  ;\n  --tw-numeric-fraction:  ;\n  --tw-ring-inset:  ;\n  --tw-ring-offset-width: 0px;\n  --tw-ring-offset-color: #fff;\n  --tw-ring-color: rgb(59 130 246 / 0.5);\n  --tw-ring-offset-shadow: 0 0 #0000;\n  --tw-ring-shadow: 0 0 #0000;\n  --tw-shadow: 0 0 #0000;\n  --tw-shadow-colored: 0 0 #0000;\n  --tw-blur:  ;\n  --tw-brightness:  ;\n  --tw-contrast:  ;\n  --tw-grayscale:  ;\n  --tw-hue-rotate:  ;\n  --tw-invert:  ;\n  --tw-saturate:  ;\n  --tw-sepia:  ;\n  --tw-drop-shadow:  ;\n  --tw-backdrop-blur:  ;\n  --tw-backdrop-brightness:  ;\n  --tw-backdrop-contrast:  ;\n  --tw-backdrop-grayscale:  ;\n  --tw-backdrop-hue-rotate:  ;\n  --tw-backdrop-invert:  ;\n  --tw-backdrop-opacity:  ;\n  --tw-backdrop-saturate:  ;\n  --tw-backdrop-sepia:  ;\n  --tw-contain-size:  ;\n  --tw-contain-layout:  ;\n  --tw-contain-paint:  ;\n  --tw-contain-style:  ;\n}/*\n! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com\n*//*\n1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\n2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)\n*/\n\n*,\n::before,\n::after {\n  box-sizing: border-box; /* 1 */\n  border-width: 0; /* 2 */\n  border-style: solid; /* 2 */\n  border-color: #e5e7eb; /* 2 */\n}\n\n::before,\n::after {\n  --tw-content: '';\n}\n\n/*\n1. Use a consistent sensible line-height in all browsers.\n2. Prevent adjustments of font size after orientation changes in iOS.\n3. Use a more readable tab size.\n4. Use the user's configured `sans` font-family by default.\n5. Use the user's configured `sans` font-feature-settings by default.\n6. Use the user's configured `sans` font-variation-settings by default.\n7. Disable tap highlights on iOS\n*/\n\nhtml,\n:host {\n  line-height: 1.5; /* 1 */\n  -webkit-text-size-adjust: 100%; /* 2 */\n  -moz-tab-size: 4; /* 3 */\n  tab-size: 4; /* 3 */\n  font-family: ui-sans-serif, system-ui, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\"; /* 4 */\n  font-feature-settings: normal; /* 5 */\n  font-variation-settings: normal; /* 6 */\n  -webkit-tap-highlight-color: transparent; /* 7 */\n}\n\n/*\n1. Remove the margin in all browsers.\n2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.\n*/\n\nbody {\n  margin: 0; /* 1 */\n  line-height: inherit; /* 2 */\n}\n\n/*\n1. Add the correct height in Firefox.\n2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\n3. Ensure horizontal rules are visible by default.\n*/\n\nhr {\n  height: 0; /* 1 */\n  color: inherit; /* 2 */\n  border-top-width: 1px; /* 3 */\n}\n\n/*\nAdd the correct text decoration in Chrome, Edge, and Safari.\n*/\n\nabbr:where([title]) {\n  text-decoration: underline dotted;\n}\n\n/*\nRemove the default font size and weight for headings.\n*/\n\nh1,\nh2,\nh3,\nh4,\nh5,\nh6 {\n  font-size: inherit;\n  font-weight: inherit;\n}\n\n/*\nReset links to optimize for opt-in styling instead of opt-out.\n*/\n\na {\n  color: inherit;\n  text-decoration: inherit;\n}\n\n/*\nAdd the correct font weight in Edge and Safari.\n*/\n\nb,\nstrong {\n  font-weight: bolder;\n}\n\n/*\n1. Use the user's configured `mono` font-family by default.\n2. Use the user's configured `mono` font-feature-settings by default.\n3. Use the user's configured `mono` font-variation-settings by default.\n4. Correct the odd `em` font sizing in all browsers.\n*/\n\ncode,\nkbd,\nsamp,\npre {\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace; /* 1 */\n  font-feature-settings: normal; /* 2 */\n  font-variation-settings: normal; /* 3 */\n  font-size: 1em; /* 4 */\n}\n\n/*\nAdd the correct font size in all browsers.\n*/\n\nsmall {\n  font-size: 80%;\n}\n\n/*\nPrevent `sub` and `sup` elements from affecting the line height in all browsers.\n*/\n\nsub,\nsup {\n  font-size: 75%;\n  line-height: 0;\n  position: relative;\n  vertical-align: baseline;\n}\n\nsub {\n  bottom: -0.25em;\n}\n\nsup {\n  top: -0.5em;\n}\n\n/*\n1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\n2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\n3. Remove gaps between table borders by default.\n*/\n\ntable {\n  text-indent: 0; /* 1 */\n  border-color: inherit; /* 2 */\n  border-collapse: collapse; /* 3 */\n}\n\n/*\n1. Change the font styles in all browsers.\n2. Remove the margin in Firefox and Safari.\n3. Remove default padding in all browsers.\n*/\n\nbutton,\ninput,\noptgroup,\nselect,\ntextarea {\n  font-family: inherit; /* 1 */\n  font-feature-settings: inherit; /* 1 */\n  font-variation-settings: inherit; /* 1 */\n  font-size: 100%; /* 1 */\n  font-weight: inherit; /* 1 */\n  line-height: inherit; /* 1 */\n  letter-spacing: inherit; /* 1 */\n  color: inherit; /* 1 */\n  margin: 0; /* 2 */\n  padding: 0; /* 3 */\n}\n\n/*\nRemove the inheritance of text transform in Edge and Firefox.\n*/\n\nbutton,\nselect {\n  text-transform: none;\n}\n\n/*\n1. Correct the inability to style clickable types in iOS and Safari.\n2. Remove default button styles.\n*/\n\nbutton,\ninput:where([type='button']),\ninput:where([type='reset']),\ninput:where([type='submit']) {\n  -webkit-appearance: button; /* 1 */\n  background-color: transparent; /* 2 */\n  background-image: none; /* 2 */\n}\n\n/*\nUse the modern Firefox focus style for all focusable elements.\n*/\n\n:-moz-focusring {\n  outline: auto;\n}\n\n/*\nRemove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\n*/\n\n:-moz-ui-invalid {\n  box-shadow: none;\n}\n\n/*\nAdd the correct vertical alignment in Chrome and Firefox.\n*/\n\nprogress {\n  vertical-align: baseline;\n}\n\n/*\nCorrect the cursor style of increment and decrement buttons in Safari.\n*/\n\n::-webkit-inner-spin-button,\n::-webkit-outer-spin-button {\n  height: auto;\n}\n\n/*\n1. Correct the odd appearance in Chrome and Safari.\n2. Correct the outline style in Safari.\n*/\n\n[type='search'] {\n  -webkit-appearance: textfield; /* 1 */\n  outline-offset: -2px; /* 2 */\n}\n\n/*\nRemove the inner padding in Chrome and Safari on macOS.\n*/\n\n::-webkit-search-decoration {\n  -webkit-appearance: none;\n}\n\n/*\n1. Correct the inability to style clickable types in iOS and Safari.\n2. Change font properties to `inherit` in Safari.\n*/\n\n::-webkit-file-upload-button {\n  -webkit-appearance: button; /* 1 */\n  font: inherit; /* 2 */\n}\n\n/*\nAdd the correct display in Chrome and Safari.\n*/\n\nsummary {\n  display: list-item;\n}\n\n/*\nRemoves the default spacing and border for appropriate elements.\n*/\n\nblockquote,\ndl,\ndd,\nh1,\nh2,\nh3,\nh4,\nh5,\nh6,\nhr,\nfigure,\np,\npre {\n  margin: 0;\n}\n\nfieldset {\n  margin: 0;\n  padding: 0;\n}\n\nlegend {\n  padding: 0;\n}\n\nol,\nul,\nmenu {\n  list-style: none;\n  margin: 0;\n  padding: 0;\n}\n\n/*\nReset default styling for dialogs.\n*/\ndialog {\n  padding: 0;\n}\n\n/*\nPrevent resizing textareas horizontally by default.\n*/\n\ntextarea {\n  resize: vertical;\n}\n\n/*\n1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\n2. Set the default placeholder color to the user's configured gray 400 color.\n*/\n\ninput::placeholder,\ntextarea::placeholder {\n  opacity: 1; /* 1 */\n  color: #9ca3af; /* 2 */\n}\n\n/*\nSet the default cursor for buttons.\n*/\n\nbutton,\n[role=\"button\"] {\n  cursor: pointer;\n}\n\n/*\nMake sure disabled buttons don't get the pointer cursor.\n*/\n:disabled {\n  cursor: default;\n}\n\n/*\n1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\n2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\n   This can trigger a poorly considered lint error in some tools but is included by design.\n*/\n\nimg,\nsvg,\nvideo,\ncanvas,\naudio,\niframe,\nembed,\nobject {\n  display: block; /* 1 */\n  vertical-align: middle; /* 2 */\n}\n\n/*\nConstrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\n*/\n\nimg,\nvideo {\n  max-width: 100%;\n  height: auto;\n}\n\n/* Make elements with the HTML hidden attribute stay hidden by default */\n[hidden]:where(:not([hidden=\"until-found\"])) {\n  display: none;\n}\r\n  :root {\r\n    --background: 0 0% 98%;\r\n    --foreground: 20 14.3% 4.1%;\r\n    --card: 0 0% 100%;\r\n    --card-foreground: 20 14.3% 4.1%;\r\n    --popover: 0 0% 100%;\r\n    --popover-foreground: 20 14.3% 4.1%;\r\n    --primary: 16 100% 66%;\r\n    --primary-foreground: 0 0% 100%;\r\n    --secondary: 0 0% 96.1%;\r\n    --secondary-foreground: 20 14.3% 4.1%;\r\n    --muted: 0 0% 96.1%;\r\n    --muted-foreground: 20 8.9% 45.1%;\r\n    --accent: 9 100% 63%;\r\n    --accent-foreground: 0 0% 100%;\r\n    --destructive: 0 84.2% 60.2%;\r\n    --destructive-foreground: 0 0% 98%;\r\n    --border: 0 0% 89.8%;\r\n    --input: 0 0% 89.8%;\r\n    --ring: 16 100% 66%;\r\n    --radius: 0.5rem;\r\n  }\r\n  .dark {\r\n    --background: 20 14.3% 4.1%;\r\n    --foreground: 0 0% 98%;\r\n    --card: 20 14.3% 4.1%;\r\n    --card-foreground: 0 0% 98%;\r\n    --popover: 20 14.3% 4.1%;\r\n    --popover-foreground: 0 0% 98%;\r\n    --primary: 16 100% 66%;\r\n    --primary-foreground: 0 0% 100%;\r\n    --secondary: 20 5.1% 14.9%;\r\n    --secondary-foreground: 0 0% 98%;\r\n    --muted: 20 5.1% 14.9%;\r\n    --muted-foreground: 0 0% 63.9%;\r\n    --accent: 9 100% 63%;\r\n    --accent-foreground: 0 0% 100%;\r\n    --destructive: 0 62.8% 30.6%;\r\n    --destructive-foreground: 0 0% 98%;\r\n    --border: 20 5.1% 14.9%;\r\n    --input: 20 5.1% 14.9%;\r\n    --ring: 16 100% 66%;\r\n  }\r\n  * {\n  border-color: hsl(var(--border));\n}\r\n  body {\n  background-color: hsl(var(--background));\n  color: hsl(var(--foreground));\n}\r\n.container {\n  width: 100%;\n  margin-right: auto;\n  margin-left: auto;\n  padding-right: 2rem;\n  padding-left: 2rem;\n}\r\n@media (min-width: 1400px) {\n\n  .container {\n    max-width: 1400px;\n  }\n}\r\n.sr-only {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  margin: -1px;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  white-space: nowrap;\n  border-width: 0;\n}\r\n.pointer-events-none {\n  pointer-events: none;\n}\r\n.pointer-events-auto {\n  pointer-events: auto;\n}\r\n.invisible {\n  visibility: hidden;\n}\r\n.fixed {\n  position: fixed;\n}\r\n.absolute {\n  position: absolute;\n}\r\n.relative {\n  position: relative;\n}\r\n.sticky {\n  position: sticky;\n}\r\n.inset-0 {\n  inset: 0px;\n}\r\n.inset-x-0 {\n  left: 0px;\n  right: 0px;\n}\r\n.inset-y-0 {\n  top: 0px;\n  bottom: 0px;\n}\r\n.-bottom-12 {\n  bottom: -3rem;\n}\r\n.-left-12 {\n  left: -3rem;\n}\r\n.-right-12 {\n  right: -3rem;\n}\r\n.-top-12 {\n  top: -3rem;\n}\r\n.bottom-0 {\n  bottom: 0px;\n}\r\n.left-0 {\n  left: 0px;\n}\r\n.left-1 {\n  left: 0.25rem;\n}\r\n.left-1\\/2 {\n  left: 50%;\n}\r\n.left-2 {\n  left: 0.5rem;\n}\r\n.left-4 {\n  left: 1rem;\n}\r\n.left-\\[50\\%\\] {\n  left: 50%;\n}\r\n.right-0 {\n  right: 0px;\n}\r\n.right-1 {\n  right: 0.25rem;\n}\r\n.right-2 {\n  right: 0.5rem;\n}\r\n.right-4 {\n  right: 1rem;\n}\r\n.top-0 {\n  top: 0px;\n}\r\n.top-1\\/2 {\n  top: 50%;\n}\r\n.top-2 {\n  top: 0.5rem;\n}\r\n.top-4 {\n  top: 1rem;\n}\r\n.top-\\[50\\%\\] {\n  top: 50%;\n}\r\n.z-10 {\n  z-index: 10;\n}\r\n.z-50 {\n  z-index: 50;\n}\r\n.z-\\[100\\] {\n  z-index: 100;\n}\r\n.-mx-1 {\n  margin-left: -0.25rem;\n  margin-right: -0.25rem;\n}\r\n.mx-2 {\n  margin-left: 0.5rem;\n  margin-right: 0.5rem;\n}\r\n.mx-auto {\n  margin-left: auto;\n  margin-right: auto;\n}\r\n.my-0\\.5 {\n  margin-top: 0.125rem;\n  margin-bottom: 0.125rem;\n}\r\n.my-1 {\n  margin-top: 0.25rem;\n  margin-bottom: 0.25rem;\n}\r\n.-ml-4 {\n  margin-left: -1rem;\n}\r\n.-mt-4 {\n  margin-top: -1rem;\n}\r\n.mb-1 {\n  margin-bottom: 0.25rem;\n}\r\n.mb-2 {\n  margin-bottom: 0.5rem;\n}\r\n.mb-4 {\n  margin-bottom: 1rem;\n}\r\n.mb-8 {\n  margin-bottom: 2rem;\n}\r\n.ml-2 {\n  margin-left: 0.5rem;\n}\r\n.ml-auto {\n  margin-left: auto;\n}\r\n.mr-2 {\n  margin-right: 0.5rem;\n}\r\n.mr-6 {\n  margin-right: 1.5rem;\n}\r\n.mt-12 {\n  margin-top: 3rem;\n}\r\n.mt-2 {\n  margin-top: 0.5rem;\n}\r\n.mt-4 {\n  margin-top: 1rem;\n}\r\n.mt-6 {\n  margin-top: 1.5rem;\n}\r\n.mt-8 {\n  margin-top: 2rem;\n}\r\n.line-clamp-3 {\n  overflow: hidden;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 3;\n}\r\n.block {\n  display: block;\n}\r\n.flex {\n  display: flex;\n}\r\n.inline-flex {\n  display: inline-flex;\n}\r\n.table {\n  display: table;\n}\r\n.grid {\n  display: grid;\n}\r\n.hidden {\n  display: none;\n}\r\n.aspect-square {\n  aspect-ratio: 1 / 1;\n}\r\n.aspect-video {\n  aspect-ratio: 16 / 9;\n}\r\n.h-10 {\n  height: 2.5rem;\n}\r\n.h-11 {\n  height: 2.75rem;\n}\r\n.h-12 {\n  height: 3rem;\n}\r\n.h-14 {\n  height: 3.5rem;\n}\r\n.h-16 {\n  height: 4rem;\n}\r\n.h-2 {\n  height: 0.5rem;\n}\r\n.h-2\\.5 {\n  height: 0.625rem;\n}\r\n.h-3\\.5 {\n  height: 0.875rem;\n}\r\n.h-32 {\n  height: 8rem;\n}\r\n.h-4 {\n  height: 1rem;\n}\r\n.h-5 {\n  height: 1.25rem;\n}\r\n.h-6 {\n  height: 1.5rem;\n}\r\n.h-7 {\n  height: 1.75rem;\n}\r\n.h-8 {\n  height: 2rem;\n}\r\n.h-9 {\n  height: 2.25rem;\n}\r\n.h-\\[1px\\] {\n  height: 1px;\n}\r\n.h-\\[60vh\\] {\n  height: 60vh;\n}\r\n.h-\\[var\\(--radix-select-trigger-height\\)\\] {\n  height: var(--radix-select-trigger-height);\n}\r\n.h-full {\n  height: 100%;\n}\r\n.h-px {\n  height: 1px;\n}\r\n.max-h-96 {\n  max-height: 24rem;\n}\r\n.max-h-screen {\n  max-height: 100vh;\n}\r\n.min-h-\\[300px\\] {\n  min-height: 300px;\n}\r\n.min-h-\\[80px\\] {\n  min-height: 80px;\n}\r\n.min-h-screen {\n  min-height: 100vh;\n}\r\n.w-0 {\n  width: 0px;\n}\r\n.w-1 {\n  width: 0.25rem;\n}\r\n.w-10 {\n  width: 2.5rem;\n}\r\n.w-11 {\n  width: 2.75rem;\n}\r\n.w-2 {\n  width: 0.5rem;\n}\r\n.w-2\\.5 {\n  width: 0.625rem;\n}\r\n.w-3\\.5 {\n  width: 0.875rem;\n}\r\n.w-3\\/4 {\n  width: 75%;\n}\r\n.w-4 {\n  width: 1rem;\n}\r\n.w-5 {\n  width: 1.25rem;\n}\r\n.w-6 {\n  width: 1.5rem;\n}\r\n.w-64 {\n  width: 16rem;\n}\r\n.w-7 {\n  width: 1.75rem;\n}\r\n.w-72 {\n  width: 18rem;\n}\r\n.w-8 {\n  width: 2rem;\n}\r\n.w-9 {\n  width: 2.25rem;\n}\r\n.w-\\[1px\\] {\n  width: 1px;\n}\r\n.w-fit {\n  width: fit-content;\n}\r\n.w-full {\n  width: 100%;\n}\r\n.min-w-0 {\n  min-width: 0px;\n}\r\n.min-w-\\[12rem\\] {\n  min-width: 12rem;\n}\r\n.min-w-\\[8rem\\] {\n  min-width: 8rem;\n}\r\n.min-w-\\[var\\(--radix-select-trigger-width\\)\\] {\n  min-width: var(--radix-select-trigger-width);\n}\r\n.max-w-2xl {\n  max-width: 42rem;\n}\r\n.max-w-4xl {\n  max-width: 56rem;\n}\r\n.max-w-lg {\n  max-width: 32rem;\n}\r\n.max-w-none {\n  max-width: none;\n}\r\n.max-w-sm {\n  max-width: 24rem;\n}\r\n.flex-1 {\n  flex: 1 1 0%;\n}\r\n.shrink-0 {\n  flex-shrink: 0;\n}\r\n.flex-grow {\n  flex-grow: 1;\n}\r\n.grow {\n  flex-grow: 1;\n}\r\n.grow-0 {\n  flex-grow: 0;\n}\r\n.basis-full {\n  flex-basis: 100%;\n}\r\n.caption-bottom {\n  caption-side: bottom;\n}\r\n.border-collapse {\n  border-collapse: collapse;\n}\r\n.-translate-x-1\\/2 {\n  --tw-translate-x: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.-translate-y-1\\/2 {\n  --tw-translate-y: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.translate-x-\\[-50\\%\\] {\n  --tw-translate-x: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.translate-y-\\[-50\\%\\] {\n  --tw-translate-y: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.rotate-90 {\n  --tw-rotate: 90deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.transform {\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n@keyframes pulse {\n\n  50% {\n    opacity: .5;\n  }\n}\r\n.animate-pulse {\n  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\n}\r\n.cursor-default {\n  cursor: default;\n}\r\n.cursor-pointer {\n  cursor: pointer;\n}\r\n.touch-none {\n  touch-action: none;\n}\r\n.select-none {\n  user-select: none;\n}\r\n.flex-col {\n  flex-direction: column;\n}\r\n.flex-col-reverse {\n  flex-direction: column-reverse;\n}\r\n.flex-wrap {\n  flex-wrap: wrap;\n}\r\n.items-start {\n  align-items: flex-start;\n}\r\n.items-end {\n  align-items: flex-end;\n}\r\n.items-center {\n  align-items: center;\n}\r\n.items-stretch {\n  align-items: stretch;\n}\r\n.justify-start {\n  justify-content: flex-start;\n}\r\n.justify-end {\n  justify-content: flex-end;\n}\r\n.justify-center {\n  justify-content: center;\n}\r\n.justify-between {\n  justify-content: space-between;\n}\r\n.gap-1 {\n  gap: 0.25rem;\n}\r\n.gap-1\\.5 {\n  gap: 0.375rem;\n}\r\n.gap-2 {\n  gap: 0.5rem;\n}\r\n.gap-4 {\n  gap: 1rem;\n}\r\n.gap-8 {\n  gap: 2rem;\n}\r\n.space-x-1 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(0.25rem * var(--tw-space-x-reverse));\n  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));\n}\r\n.space-x-2 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(0.5rem * var(--tw-space-x-reverse));\n  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\n}\r\n.space-x-4 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(1rem * var(--tw-space-x-reverse));\n  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\n}\r\n.space-x-6 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(1.5rem * var(--tw-space-x-reverse));\n  margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));\n}\r\n.space-y-1 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));\n}\r\n.space-y-1\\.5 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.375rem * var(--tw-space-y-reverse));\n}\r\n.space-y-2 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));\n}\r\n.space-y-4 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(1rem * var(--tw-space-y-reverse));\n}\r\n.overflow-auto {\n  overflow: auto;\n}\r\n.overflow-hidden {\n  overflow: hidden;\n}\r\n.whitespace-nowrap {\n  white-space: nowrap;\n}\r\n.rounded-\\[2px\\] {\n  border-radius: 2px;\n}\r\n.rounded-\\[inherit\\] {\n  border-radius: inherit;\n}\r\n.rounded-full {\n  border-radius: 9999px;\n}\r\n.rounded-lg {\n  border-radius: var(--radius);\n}\r\n.rounded-md {\n  border-radius: calc(var(--radius) - 2px);\n}\r\n.rounded-sm {\n  border-radius: calc(var(--radius) - 4px);\n}\r\n.border {\n  border-width: 1px;\n}\r\n.border-2 {\n  border-width: 2px;\n}\r\n.border-\\[1\\.5px\\] {\n  border-width: 1.5px;\n}\r\n.border-b {\n  border-bottom-width: 1px;\n}\r\n.border-l {\n  border-left-width: 1px;\n}\r\n.border-r {\n  border-right-width: 1px;\n}\r\n.border-t {\n  border-top-width: 1px;\n}\r\n.border-dashed {\n  border-style: dashed;\n}\r\n.border-\\[--color-border\\] {\n  border-color: var(--color-border);\n}\r\n.border-border\\/50 {\n  border-color: hsl(var(--border) / 0.5);\n}\r\n.border-destructive {\n  border-color: hsl(var(--destructive));\n}\r\n.border-destructive\\/50 {\n  border-color: hsl(var(--destructive) / 0.5);\n}\r\n.border-green-400 {\n  --tw-border-opacity: 1;\n  border-color: rgb(74 222 128 / var(--tw-border-opacity, 1));\n}\r\n.border-input {\n  border-color: hsl(var(--input));\n}\r\n.border-primary {\n  border-color: hsl(var(--primary));\n}\r\n.border-primary\\/20 {\n  border-color: hsl(var(--primary) / 0.2);\n}\r\n.border-transparent {\n  border-color: transparent;\n}\r\n.border-l-transparent {\n  border-left-color: transparent;\n}\r\n.border-t-transparent {\n  border-top-color: transparent;\n}\r\n.bg-\\[--color-bg\\] {\n  background-color: var(--color-bg);\n}\r\n.bg-accent {\n  background-color: hsl(var(--accent));\n}\r\n.bg-background {\n  background-color: hsl(var(--background));\n}\r\n.bg-background\\/95 {\n  background-color: hsl(var(--background) / 0.95);\n}\r\n.bg-black\\/60 {\n  background-color: rgb(0 0 0 / 0.6);\n}\r\n.bg-black\\/80 {\n  background-color: rgb(0 0 0 / 0.8);\n}\r\n.bg-border {\n  background-color: hsl(var(--border));\n}\r\n.bg-card {\n  background-color: hsl(var(--card));\n}\r\n.bg-destructive {\n  background-color: hsl(var(--destructive));\n}\r\n.bg-green-500\\/20 {\n  background-color: rgb(34 197 94 / 0.2);\n}\r\n.bg-muted {\n  background-color: hsl(var(--muted));\n}\r\n.bg-muted\\/40 {\n  background-color: hsl(var(--muted) / 0.4);\n}\r\n.bg-muted\\/50 {\n  background-color: hsl(var(--muted) / 0.5);\n}\r\n.bg-popover {\n  background-color: hsl(var(--popover));\n}\r\n.bg-primary {\n  background-color: hsl(var(--primary));\n}\r\n.bg-primary\\/10 {\n  background-color: hsl(var(--primary) / 0.1);\n}\r\n.bg-secondary {\n  background-color: hsl(var(--secondary));\n}\r\n.bg-transparent {\n  background-color: transparent;\n}\r\n.bg-gradient-to-b {\n  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));\n}\r\n.from-white {\n  --tw-gradient-from: #fff var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\r\n.to-background {\n  --tw-gradient-to: hsl(var(--background)) var(--tw-gradient-to-position);\n}\r\n.fill-current {\n  fill: currentColor;\n}\r\n.object-cover {\n  object-fit: cover;\n}\r\n.p-0 {\n  padding: 0px;\n}\r\n.p-1 {\n  padding: 0.25rem;\n}\r\n.p-3 {\n  padding: 0.75rem;\n}\r\n.p-4 {\n  padding: 1rem;\n}\r\n.p-6 {\n  padding: 1.5rem;\n}\r\n.p-8 {\n  padding: 2rem;\n}\r\n.p-\\[1px\\] {\n  padding: 1px;\n}\r\n.px-2 {\n  padding-left: 0.5rem;\n  padding-right: 0.5rem;\n}\r\n.px-2\\.5 {\n  padding-left: 0.625rem;\n  padding-right: 0.625rem;\n}\r\n.px-3 {\n  padding-left: 0.75rem;\n  padding-right: 0.75rem;\n}\r\n.px-4 {\n  padding-left: 1rem;\n  padding-right: 1rem;\n}\r\n.px-8 {\n  padding-left: 2rem;\n  padding-right: 2rem;\n}\r\n.py-0\\.5 {\n  padding-top: 0.125rem;\n  padding-bottom: 0.125rem;\n}\r\n.py-1 {\n  padding-top: 0.25rem;\n  padding-bottom: 0.25rem;\n}\r\n.py-1\\.5 {\n  padding-top: 0.375rem;\n  padding-bottom: 0.375rem;\n}\r\n.py-10 {\n  padding-top: 2.5rem;\n  padding-bottom: 2.5rem;\n}\r\n.py-12 {\n  padding-top: 3rem;\n  padding-bottom: 3rem;\n}\r\n.py-16 {\n  padding-top: 4rem;\n  padding-bottom: 4rem;\n}\r\n.py-2 {\n  padding-top: 0.5rem;\n  padding-bottom: 0.5rem;\n}\r\n.py-4 {\n  padding-top: 1rem;\n  padding-bottom: 1rem;\n}\r\n.pb-3 {\n  padding-bottom: 0.75rem;\n}\r\n.pb-4 {\n  padding-bottom: 1rem;\n}\r\n.pb-6 {\n  padding-bottom: 1.5rem;\n}\r\n.pl-4 {\n  padding-left: 1rem;\n}\r\n.pl-8 {\n  padding-left: 2rem;\n}\r\n.pr-2 {\n  padding-right: 0.5rem;\n}\r\n.pr-8 {\n  padding-right: 2rem;\n}\r\n.pt-0 {\n  padding-top: 0px;\n}\r\n.pt-1 {\n  padding-top: 0.25rem;\n}\r\n.pt-3 {\n  padding-top: 0.75rem;\n}\r\n.pt-4 {\n  padding-top: 1rem;\n}\r\n.pt-5 {\n  padding-top: 1.25rem;\n}\r\n.pt-6 {\n  padding-top: 1.5rem;\n}\r\n.text-left {\n  text-align: left;\n}\r\n.text-center {\n  text-align: center;\n}\r\n.align-middle {\n  vertical-align: middle;\n}\r\n.font-body {\n  font-family: Inter, sans-serif;\n}\r\n.font-headline {\n  font-family: 'Space Grotesk', sans-serif;\n}\r\n.font-mono {\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace;\n}\r\n.text-2xl {\n  font-size: 1.5rem;\n  line-height: 2rem;\n}\r\n.text-3xl {\n  font-size: 1.875rem;\n  line-height: 2.25rem;\n}\r\n.text-4xl {\n  font-size: 2.25rem;\n  line-height: 2.5rem;\n}\r\n.text-\\[0\\.8rem\\] {\n  font-size: 0.8rem;\n}\r\n.text-base {\n  font-size: 1rem;\n  line-height: 1.5rem;\n}\r\n.text-lg {\n  font-size: 1.125rem;\n  line-height: 1.75rem;\n}\r\n.text-sm {\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n}\r\n.text-xl {\n  font-size: 1.25rem;\n  line-height: 1.75rem;\n}\r\n.text-xs {\n  font-size: 0.75rem;\n  line-height: 1rem;\n}\r\n.font-bold {\n  font-weight: 700;\n}\r\n.font-medium {\n  font-weight: 500;\n}\r\n.font-normal {\n  font-weight: 400;\n}\r\n.font-semibold {\n  font-weight: 600;\n}\r\n.tabular-nums {\n  --tw-numeric-spacing: tabular-nums;\n  font-variant-numeric: var(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure) var(--tw-numeric-spacing) var(--tw-numeric-fraction);\n}\r\n.leading-8 {\n  line-height: 2rem;\n}\r\n.leading-loose {\n  line-height: 2;\n}\r\n.leading-none {\n  line-height: 1;\n}\r\n.leading-tight {\n  line-height: 1.25;\n}\r\n.tracking-tight {\n  letter-spacing: -0.025em;\n}\r\n.tracking-widest {\n  letter-spacing: 0.1em;\n}\r\n.text-accent-foreground {\n  color: hsl(var(--accent-foreground));\n}\r\n.text-card-foreground {\n  color: hsl(var(--card-foreground));\n}\r\n.text-current {\n  color: currentColor;\n}\r\n.text-destructive {\n  color: hsl(var(--destructive));\n}\r\n.text-destructive-foreground {\n  color: hsl(var(--destructive-foreground));\n}\r\n.text-foreground {\n  color: hsl(var(--foreground));\n}\r\n.text-foreground\\/50 {\n  color: hsl(var(--foreground) / 0.5);\n}\r\n.text-foreground\\/80 {\n  color: hsl(var(--foreground) / 0.8);\n}\r\n.text-green-700 {\n  --tw-text-opacity: 1;\n  color: rgb(21 128 61 / var(--tw-text-opacity, 1));\n}\r\n.text-muted-foreground {\n  color: hsl(var(--muted-foreground));\n}\r\n.text-neutral-200 {\n  --tw-text-opacity: 1;\n  color: rgb(229 229 229 / var(--tw-text-opacity, 1));\n}\r\n.text-popover-foreground {\n  color: hsl(var(--popover-foreground));\n}\r\n.text-primary {\n  color: hsl(var(--primary));\n}\r\n.text-primary-foreground {\n  color: hsl(var(--primary-foreground));\n}\r\n.text-secondary-foreground {\n  color: hsl(var(--secondary-foreground));\n}\r\n.text-white {\n  --tw-text-opacity: 1;\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n}\r\n.underline-offset-4 {\n  text-underline-offset: 4px;\n}\r\n.antialiased {\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\r\n.opacity-0 {\n  opacity: 0;\n}\r\n.opacity-50 {\n  opacity: 0.5;\n}\r\n.opacity-60 {\n  opacity: 0.6;\n}\r\n.opacity-70 {\n  opacity: 0.7;\n}\r\n.opacity-90 {\n  opacity: 0.9;\n}\r\n.shadow-lg {\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.shadow-md {\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.shadow-sm {\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.shadow-xl {\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.outline-none {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\r\n.outline {\n  outline-style: solid;\n}\r\n.ring-0 {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\r\n.ring-offset-background {\n  --tw-ring-offset-color: hsl(var(--background));\n}\r\n.filter {\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\r\n.backdrop-blur {\n  --tw-backdrop-blur: blur(8px);\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n}\r\n.backdrop-filter {\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n}\r\n.transition {\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\r\n.transition-all {\n  transition-property: all;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\r\n.transition-colors {\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\r\n.transition-opacity {\n  transition-property: opacity;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\r\n.transition-transform {\n  transition-property: transform;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\r\n.duration-200 {\n  transition-duration: 200ms;\n}\r\n.duration-300 {\n  transition-duration: 300ms;\n}\r\n.ease-in-out {\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n}\r\n@keyframes enter {\n\n  from {\n    opacity: var(--tw-enter-opacity, 1);\n    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));\n  }\n}\r\n@keyframes exit {\n\n  to {\n    opacity: var(--tw-exit-opacity, 1);\n    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));\n  }\n}\r\n.animate-in {\n  animation-name: enter;\n  animation-duration: 150ms;\n  --tw-enter-opacity: initial;\n  --tw-enter-scale: initial;\n  --tw-enter-rotate: initial;\n  --tw-enter-translate-x: initial;\n  --tw-enter-translate-y: initial;\n}\r\n.fade-in-0 {\n  --tw-enter-opacity: 0;\n}\r\n.zoom-in-95 {\n  --tw-enter-scale: .95;\n}\r\n.duration-200 {\n  animation-duration: 200ms;\n}\r\n.duration-300 {\n  animation-duration: 300ms;\n}\r\n.ease-in-out {\n  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n}\r\n.file\\:border-0::file-selector-button {\n  border-width: 0px;\n}\r\n.file\\:bg-transparent::file-selector-button {\n  background-color: transparent;\n}\r\n.file\\:text-sm::file-selector-button {\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n}\r\n.file\\:font-medium::file-selector-button {\n  font-weight: 500;\n}\r\n.file\\:text-foreground::file-selector-button {\n  color: hsl(var(--foreground));\n}\r\n.placeholder\\:text-muted-foreground::placeholder {\n  color: hsl(var(--muted-foreground));\n}\r\n.focus-within\\:relative:focus-within {\n  position: relative;\n}\r\n.focus-within\\:z-20:focus-within {\n  z-index: 20;\n}\r\n.hover\\:-translate-y-1:hover {\n  --tw-translate-y: -0.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.hover\\:scale-105:hover {\n  --tw-scale-x: 1.05;\n  --tw-scale-y: 1.05;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.hover\\:bg-accent:hover {\n  background-color: hsl(var(--accent));\n}\r\n.hover\\:bg-destructive\\/80:hover {\n  background-color: hsl(var(--destructive) / 0.8);\n}\r\n.hover\\:bg-destructive\\/90:hover {\n  background-color: hsl(var(--destructive) / 0.9);\n}\r\n.hover\\:bg-green-500\\/30:hover {\n  background-color: rgb(34 197 94 / 0.3);\n}\r\n.hover\\:bg-muted\\/50:hover {\n  background-color: hsl(var(--muted) / 0.5);\n}\r\n.hover\\:bg-muted\\/75:hover {\n  background-color: hsl(var(--muted) / 0.75);\n}\r\n.hover\\:bg-primary:hover {\n  background-color: hsl(var(--primary));\n}\r\n.hover\\:bg-primary\\/80:hover {\n  background-color: hsl(var(--primary) / 0.8);\n}\r\n.hover\\:bg-primary\\/90:hover {\n  background-color: hsl(var(--primary) / 0.9);\n}\r\n.hover\\:bg-secondary:hover {\n  background-color: hsl(var(--secondary));\n}\r\n.hover\\:bg-secondary\\/80:hover {\n  background-color: hsl(var(--secondary) / 0.8);\n}\r\n.hover\\:text-accent:hover {\n  color: hsl(var(--accent));\n}\r\n.hover\\:text-accent-foreground:hover {\n  color: hsl(var(--accent-foreground));\n}\r\n.hover\\:text-foreground:hover {\n  color: hsl(var(--foreground));\n}\r\n.hover\\:text-primary:hover {\n  color: hsl(var(--primary));\n}\r\n.hover\\:text-primary-foreground:hover {\n  color: hsl(var(--primary-foreground));\n}\r\n.hover\\:underline:hover {\n  text-decoration-line: underline;\n}\r\n.hover\\:opacity-100:hover {\n  opacity: 1;\n}\r\n.hover\\:shadow-lg:hover {\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.hover\\:shadow-xl:hover {\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.focus\\:bg-accent:focus {\n  background-color: hsl(var(--accent));\n}\r\n.focus\\:bg-primary:focus {\n  background-color: hsl(var(--primary));\n}\r\n.focus\\:text-accent-foreground:focus {\n  color: hsl(var(--accent-foreground));\n}\r\n.focus\\:text-primary-foreground:focus {\n  color: hsl(var(--primary-foreground));\n}\r\n.focus\\:opacity-100:focus {\n  opacity: 1;\n}\r\n.focus\\:outline-none:focus {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\r\n.focus\\:ring-2:focus {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\r\n.focus\\:ring-ring:focus {\n  --tw-ring-color: hsl(var(--ring));\n}\r\n.focus\\:ring-offset-2:focus {\n  --tw-ring-offset-width: 2px;\n}\r\n.focus-visible\\:outline-none:focus-visible {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\r\n.focus-visible\\:ring-2:focus-visible {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\r\n.focus-visible\\:ring-ring:focus-visible {\n  --tw-ring-color: hsl(var(--ring));\n}\r\n.focus-visible\\:ring-offset-2:focus-visible {\n  --tw-ring-offset-width: 2px;\n}\r\n.focus-visible\\:ring-offset-background:focus-visible {\n  --tw-ring-offset-color: hsl(var(--background));\n}\r\n.disabled\\:pointer-events-none:disabled {\n  pointer-events: none;\n}\r\n.disabled\\:cursor-not-allowed:disabled {\n  cursor: not-allowed;\n}\r\n.disabled\\:opacity-50:disabled {\n  opacity: 0.5;\n}\r\n.group:hover .group-hover\\:opacity-100 {\n  opacity: 1;\n}\r\n.group.destructive .group-\\[\\.destructive\\]\\:border-muted\\/40 {\n  border-color: hsl(var(--muted) / 0.4);\n}\r\n.group.destructive .group-\\[\\.destructive\\]\\:text-red-300 {\n  --tw-text-opacity: 1;\n  color: rgb(252 165 165 / var(--tw-text-opacity, 1));\n}\r\n.group.destructive .group-\\[\\.destructive\\]\\:hover\\:border-destructive\\/30:hover {\n  border-color: hsl(var(--destructive) / 0.3);\n}\r\n.group.destructive .group-\\[\\.destructive\\]\\:hover\\:bg-destructive:hover {\n  background-color: hsl(var(--destructive));\n}\r\n.group.destructive .group-\\[\\.destructive\\]\\:hover\\:text-destructive-foreground:hover {\n  color: hsl(var(--destructive-foreground));\n}\r\n.group.destructive .group-\\[\\.destructive\\]\\:hover\\:text-red-50:hover {\n  --tw-text-opacity: 1;\n  color: rgb(254 242 242 / var(--tw-text-opacity, 1));\n}\r\n.group.destructive .group-\\[\\.destructive\\]\\:focus\\:ring-destructive:focus {\n  --tw-ring-color: hsl(var(--destructive));\n}\r\n.group.destructive .group-\\[\\.destructive\\]\\:focus\\:ring-red-400:focus {\n  --tw-ring-opacity: 1;\n  --tw-ring-color: rgb(248 113 113 / var(--tw-ring-opacity, 1));\n}\r\n.group.destructive .group-\\[\\.destructive\\]\\:focus\\:ring-offset-red-600:focus {\n  --tw-ring-offset-color: #dc2626;\n}\r\n.peer:disabled ~ .peer-disabled\\:cursor-not-allowed {\n  cursor: not-allowed;\n}\r\n.peer:disabled ~ .peer-disabled\\:opacity-70 {\n  opacity: 0.7;\n}\r\n.aria-selected\\:bg-accent[aria-selected=\"true\"] {\n  background-color: hsl(var(--accent));\n}\r\n.aria-selected\\:bg-accent\\/50[aria-selected=\"true\"] {\n  background-color: hsl(var(--accent) / 0.5);\n}\r\n.aria-selected\\:text-accent-foreground[aria-selected=\"true\"] {\n  color: hsl(var(--accent-foreground));\n}\r\n.aria-selected\\:text-muted-foreground[aria-selected=\"true\"] {\n  color: hsl(var(--muted-foreground));\n}\r\n.aria-selected\\:opacity-100[aria-selected=\"true\"] {\n  opacity: 1;\n}\r\n.data-\\[disabled\\]\\:pointer-events-none[data-disabled] {\n  pointer-events: none;\n}\r\n.data-\\[side\\=bottom\\]\\:translate-y-1[data-side=\"bottom\"] {\n  --tw-translate-y: 0.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.data-\\[side\\=left\\]\\:-translate-x-1[data-side=\"left\"] {\n  --tw-translate-x: -0.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.data-\\[side\\=right\\]\\:translate-x-1[data-side=\"right\"] {\n  --tw-translate-x: 0.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.data-\\[side\\=top\\]\\:-translate-y-1[data-side=\"top\"] {\n  --tw-translate-y: -0.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.data-\\[state\\=checked\\]\\:translate-x-5[data-state=\"checked\"] {\n  --tw-translate-x: 1.25rem;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.data-\\[state\\=unchecked\\]\\:translate-x-0[data-state=\"unchecked\"] {\n  --tw-translate-x: 0px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.data-\\[swipe\\=cancel\\]\\:translate-x-0[data-swipe=\"cancel\"] {\n  --tw-translate-x: 0px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.data-\\[swipe\\=end\\]\\:translate-x-\\[var\\(--radix-toast-swipe-end-x\\)\\][data-swipe=\"end\"] {\n  --tw-translate-x: var(--radix-toast-swipe-end-x);\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.data-\\[swipe\\=move\\]\\:translate-x-\\[var\\(--radix-toast-swipe-move-x\\)\\][data-swipe=\"move\"] {\n  --tw-translate-x: var(--radix-toast-swipe-move-x);\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n@keyframes accordion-up {\n\n  from {\n    height: var(--radix-accordion-content-height);\n  }\n\n  to {\n    height: 0;\n  }\n}\r\n.data-\\[state\\=closed\\]\\:animate-accordion-up[data-state=\"closed\"] {\n  animation: accordion-up 0.2s ease-out;\n}\r\n@keyframes accordion-down {\n\n  from {\n    height: 0;\n  }\n\n  to {\n    height: var(--radix-accordion-content-height);\n  }\n}\r\n.data-\\[state\\=open\\]\\:animate-accordion-down[data-state=\"open\"] {\n  animation: accordion-down 0.2s ease-out;\n}\r\n.data-\\[state\\=active\\]\\:bg-background[data-state=\"active\"] {\n  background-color: hsl(var(--background));\n}\r\n.data-\\[state\\=checked\\]\\:bg-primary[data-state=\"checked\"] {\n  background-color: hsl(var(--primary));\n}\r\n.data-\\[state\\=open\\]\\:bg-accent[data-state=\"open\"] {\n  background-color: hsl(var(--accent));\n}\r\n.data-\\[state\\=open\\]\\:bg-secondary[data-state=\"open\"] {\n  background-color: hsl(var(--secondary));\n}\r\n.data-\\[state\\=selected\\]\\:bg-muted[data-state=\"selected\"] {\n  background-color: hsl(var(--muted));\n}\r\n.data-\\[state\\=unchecked\\]\\:bg-input[data-state=\"unchecked\"] {\n  background-color: hsl(var(--input));\n}\r\n.data-\\[state\\=active\\]\\:text-foreground[data-state=\"active\"] {\n  color: hsl(var(--foreground));\n}\r\n.data-\\[state\\=checked\\]\\:text-primary-foreground[data-state=\"checked\"] {\n  color: hsl(var(--primary-foreground));\n}\r\n.data-\\[state\\=open\\]\\:text-accent-foreground[data-state=\"open\"] {\n  color: hsl(var(--accent-foreground));\n}\r\n.data-\\[state\\=open\\]\\:text-muted-foreground[data-state=\"open\"] {\n  color: hsl(var(--muted-foreground));\n}\r\n.data-\\[disabled\\]\\:opacity-50[data-disabled] {\n  opacity: 0.5;\n}\r\n.data-\\[state\\=active\\]\\:shadow-sm[data-state=\"active\"] {\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\r\n.data-\\[swipe\\=move\\]\\:transition-none[data-swipe=\"move\"] {\n  transition-property: none;\n}\r\n.data-\\[state\\=closed\\]\\:duration-300[data-state=\"closed\"] {\n  transition-duration: 300ms;\n}\r\n.data-\\[state\\=open\\]\\:duration-500[data-state=\"open\"] {\n  transition-duration: 500ms;\n}\r\n.data-\\[state\\=open\\]\\:animate-in[data-state=\"open\"] {\n  animation-name: enter;\n  animation-duration: 150ms;\n  --tw-enter-opacity: initial;\n  --tw-enter-scale: initial;\n  --tw-enter-rotate: initial;\n  --tw-enter-translate-x: initial;\n  --tw-enter-translate-y: initial;\n}\r\n.data-\\[state\\=closed\\]\\:animate-out[data-state=\"closed\"] {\n  animation-name: exit;\n  animation-duration: 150ms;\n  --tw-exit-opacity: initial;\n  --tw-exit-scale: initial;\n  --tw-exit-rotate: initial;\n  --tw-exit-translate-x: initial;\n  --tw-exit-translate-y: initial;\n}\r\n.data-\\[swipe\\=end\\]\\:animate-out[data-swipe=\"end\"] {\n  animation-name: exit;\n  animation-duration: 150ms;\n  --tw-exit-opacity: initial;\n  --tw-exit-scale: initial;\n  --tw-exit-rotate: initial;\n  --tw-exit-translate-x: initial;\n  --tw-exit-translate-y: initial;\n}\r\n.data-\\[state\\=closed\\]\\:fade-out-0[data-state=\"closed\"] {\n  --tw-exit-opacity: 0;\n}\r\n.data-\\[state\\=closed\\]\\:fade-out-80[data-state=\"closed\"] {\n  --tw-exit-opacity: 0.8;\n}\r\n.data-\\[state\\=open\\]\\:fade-in-0[data-state=\"open\"] {\n  --tw-enter-opacity: 0;\n}\r\n.data-\\[state\\=closed\\]\\:zoom-out-95[data-state=\"closed\"] {\n  --tw-exit-scale: .95;\n}\r\n.data-\\[state\\=open\\]\\:zoom-in-95[data-state=\"open\"] {\n  --tw-enter-scale: .95;\n}\r\n.data-\\[side\\=bottom\\]\\:slide-in-from-top-2[data-side=\"bottom\"] {\n  --tw-enter-translate-y: -0.5rem;\n}\r\n.data-\\[side\\=left\\]\\:slide-in-from-right-2[data-side=\"left\"] {\n  --tw-enter-translate-x: 0.5rem;\n}\r\n.data-\\[side\\=right\\]\\:slide-in-from-left-2[data-side=\"right\"] {\n  --tw-enter-translate-x: -0.5rem;\n}\r\n.data-\\[side\\=top\\]\\:slide-in-from-bottom-2[data-side=\"top\"] {\n  --tw-enter-translate-y: 0.5rem;\n}\r\n.data-\\[state\\=closed\\]\\:slide-out-to-bottom[data-state=\"closed\"] {\n  --tw-exit-translate-y: 100%;\n}\r\n.data-\\[state\\=closed\\]\\:slide-out-to-left[data-state=\"closed\"] {\n  --tw-exit-translate-x: -100%;\n}\r\n.data-\\[state\\=closed\\]\\:slide-out-to-left-1\\/2[data-state=\"closed\"] {\n  --tw-exit-translate-x: -50%;\n}\r\n.data-\\[state\\=closed\\]\\:slide-out-to-right[data-state=\"closed\"] {\n  --tw-exit-translate-x: 100%;\n}\r\n.data-\\[state\\=closed\\]\\:slide-out-to-right-full[data-state=\"closed\"] {\n  --tw-exit-translate-x: 100%;\n}\r\n.data-\\[state\\=closed\\]\\:slide-out-to-top[data-state=\"closed\"] {\n  --tw-exit-translate-y: -100%;\n}\r\n.data-\\[state\\=closed\\]\\:slide-out-to-top-\\[48\\%\\][data-state=\"closed\"] {\n  --tw-exit-translate-y: -48%;\n}\r\n.data-\\[state\\=open\\]\\:slide-in-from-bottom[data-state=\"open\"] {\n  --tw-enter-translate-y: 100%;\n}\r\n.data-\\[state\\=open\\]\\:slide-in-from-left[data-state=\"open\"] {\n  --tw-enter-translate-x: -100%;\n}\r\n.data-\\[state\\=open\\]\\:slide-in-from-left-1\\/2[data-state=\"open\"] {\n  --tw-enter-translate-x: -50%;\n}\r\n.data-\\[state\\=open\\]\\:slide-in-from-right[data-state=\"open\"] {\n  --tw-enter-translate-x: 100%;\n}\r\n.data-\\[state\\=open\\]\\:slide-in-from-top[data-state=\"open\"] {\n  --tw-enter-translate-y: -100%;\n}\r\n.data-\\[state\\=open\\]\\:slide-in-from-top-\\[48\\%\\][data-state=\"open\"] {\n  --tw-enter-translate-y: -48%;\n}\r\n.data-\\[state\\=open\\]\\:slide-in-from-top-full[data-state=\"open\"] {\n  --tw-enter-translate-y: -100%;\n}\r\n.data-\\[state\\=closed\\]\\:duration-300[data-state=\"closed\"] {\n  animation-duration: 300ms;\n}\r\n.data-\\[state\\=open\\]\\:duration-500[data-state=\"open\"] {\n  animation-duration: 500ms;\n}\r\n@supports (backdrop-filter: var(--tw)) {\n\n  .supports-\\[backdrop-filter\\]\\:bg-background\\/60 {\n    background-color: hsl(var(--background) / 0.6);\n  }\n}\r\n.dark\\:border-destructive:is(.dark *) {\n  border-color: hsl(var(--destructive));\n}\r\n@media (min-width: 640px) {\n\n  .sm\\:bottom-0 {\n    bottom: 0px;\n  }\n\n  .sm\\:right-0 {\n    right: 0px;\n  }\n\n  .sm\\:top-auto {\n    top: auto;\n  }\n\n  .sm\\:mt-0 {\n    margin-top: 0px;\n  }\n\n  .sm\\:max-w-sm {\n    max-width: 24rem;\n  }\n\n  .sm\\:flex-row {\n    flex-direction: row;\n  }\n\n  .sm\\:flex-col {\n    flex-direction: column;\n  }\n\n  .sm\\:justify-end {\n    justify-content: flex-end;\n  }\n\n  .sm\\:space-x-2 > :not([hidden]) ~ :not([hidden]) {\n    --tw-space-x-reverse: 0;\n    margin-right: calc(0.5rem * var(--tw-space-x-reverse));\n    margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\n  }\n\n  .sm\\:space-x-4 > :not([hidden]) ~ :not([hidden]) {\n    --tw-space-x-reverse: 0;\n    margin-right: calc(1rem * var(--tw-space-x-reverse));\n    margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\n  }\n\n  .sm\\:space-y-0 > :not([hidden]) ~ :not([hidden]) {\n    --tw-space-y-reverse: 0;\n    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));\n    margin-bottom: calc(0px * var(--tw-space-y-reverse));\n  }\n\n  .sm\\:rounded-lg {\n    border-radius: var(--radius);\n  }\n\n  .sm\\:text-left {\n    text-align: left;\n  }\n\n  .sm\\:text-4xl {\n    font-size: 2.25rem;\n    line-height: 2.5rem;\n  }\n\n  .data-\\[state\\=open\\]\\:sm\\:slide-in-from-bottom-full[data-state=\"open\"] {\n    --tw-enter-translate-y: 100%;\n  }\n}\r\n@media (min-width: 768px) {\n\n  .md\\:block {\n    display: block;\n  }\n\n  .md\\:flex {\n    display: flex;\n  }\n\n  .md\\:h-24 {\n    height: 6rem;\n  }\n\n  .md\\:max-w-\\[420px\\] {\n    max-width: 420px;\n  }\n\n  .md\\:grid-cols-2 {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n\n  .md\\:grid-cols-3 {\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n\n  .md\\:flex-row {\n    flex-direction: row;\n  }\n\n  .md\\:gap-2 {\n    gap: 0.5rem;\n  }\n\n  .md\\:px-0 {\n    padding-left: 0px;\n    padding-right: 0px;\n  }\n\n  .md\\:py-0 {\n    padding-top: 0px;\n    padding-bottom: 0px;\n  }\n\n  .md\\:py-20 {\n    padding-top: 5rem;\n    padding-bottom: 5rem;\n  }\n\n  .md\\:py-24 {\n    padding-top: 6rem;\n    padding-bottom: 6rem;\n  }\n\n  .md\\:text-left {\n    text-align: left;\n  }\n\n  .md\\:text-5xl {\n    font-size: 3rem;\n    line-height: 1;\n  }\n\n  .md\\:text-6xl {\n    font-size: 3.75rem;\n    line-height: 1;\n  }\n\n  .md\\:text-sm {\n    font-size: 0.875rem;\n    line-height: 1.25rem;\n  }\n\n  .md\\:text-xl {\n    font-size: 1.25rem;\n    line-height: 1.75rem;\n  }\n}\r\n@media (min-width: 1024px) {\n\n  .lg\\:grid-cols-3 {\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n\n  .lg\\:grid-cols-5 {\n    grid-template-columns: repeat(5, minmax(0, 1fr));\n  }\n}\r\n.\\[\\&\\:has\\(\\[aria-selected\\]\\)\\]\\:bg-accent:has([aria-selected]) {\n  background-color: hsl(var(--accent));\n}\r\n.first\\:\\[\\&\\:has\\(\\[aria-selected\\]\\)\\]\\:rounded-l-md:has([aria-selected]):first-child {\n  border-top-left-radius: calc(var(--radius) - 2px);\n  border-bottom-left-radius: calc(var(--radius) - 2px);\n}\r\n.last\\:\\[\\&\\:has\\(\\[aria-selected\\]\\)\\]\\:rounded-r-md:has([aria-selected]):last-child {\n  border-top-right-radius: calc(var(--radius) - 2px);\n  border-bottom-right-radius: calc(var(--radius) - 2px);\n}\r\n.\\[\\&\\:has\\(\\[aria-selected\\]\\.day-outside\\)\\]\\:bg-accent\\/50:has([aria-selected].day-outside) {\n  background-color: hsl(var(--accent) / 0.5);\n}\r\n.\\[\\&\\:has\\(\\[aria-selected\\]\\.day-range-end\\)\\]\\:rounded-r-md:has([aria-selected].day-range-end) {\n  border-top-right-radius: calc(var(--radius) - 2px);\n  border-bottom-right-radius: calc(var(--radius) - 2px);\n}\r\n.\\[\\&\\:has\\(\\[role\\=checkbox\\]\\)\\]\\:pr-0:has([role=checkbox]) {\n  padding-right: 0px;\n}\r\n.\\[\\&\\>span\\]\\:line-clamp-1>span {\n  overflow: hidden;\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 1;\n}\r\n.\\[\\&\\>svg\\+div\\]\\:translate-y-\\[-3px\\]>svg+div {\n  --tw-translate-y: -3px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.\\[\\&\\>svg\\]\\:absolute>svg {\n  position: absolute;\n}\r\n.\\[\\&\\>svg\\]\\:left-4>svg {\n  left: 1rem;\n}\r\n.\\[\\&\\>svg\\]\\:top-4>svg {\n  top: 1rem;\n}\r\n.\\[\\&\\>svg\\]\\:h-2\\.5>svg {\n  height: 0.625rem;\n}\r\n.\\[\\&\\>svg\\]\\:h-3>svg {\n  height: 0.75rem;\n}\r\n.\\[\\&\\>svg\\]\\:w-2\\.5>svg {\n  width: 0.625rem;\n}\r\n.\\[\\&\\>svg\\]\\:w-3>svg {\n  width: 0.75rem;\n}\r\n.\\[\\&\\>svg\\]\\:text-destructive>svg {\n  color: hsl(var(--destructive));\n}\r\n.\\[\\&\\>svg\\]\\:text-foreground>svg {\n  color: hsl(var(--foreground));\n}\r\n.\\[\\&\\>svg\\]\\:text-muted-foreground>svg {\n  color: hsl(var(--muted-foreground));\n}\r\n.\\[\\&\\>svg\\~\\*\\]\\:pl-7>svg~* {\n  padding-left: 1.75rem;\n}\r\n.\\[\\&\\>tr\\]\\:last\\:border-b-0:last-child>tr {\n  border-bottom-width: 0px;\n}\r\n.\\[\\&\\[data-state\\=open\\]\\>svg\\]\\:rotate-180[data-state=open]>svg {\n  --tw-rotate: 180deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\r\n.\\[\\&_\\.recharts-cartesian-axis-tick_text\\]\\:fill-muted-foreground .recharts-cartesian-axis-tick text {\n  fill: hsl(var(--muted-foreground));\n}\r\n.\\[\\&_\\.recharts-cartesian-grid_line\\[stroke\\=\\'\\#ccc\\'\\]\\]\\:stroke-border\\/50 .recharts-cartesian-grid line[stroke='#ccc'] {\n  stroke: hsl(var(--border) / 0.5);\n}\r\n.\\[\\&_\\.recharts-curve\\.recharts-tooltip-cursor\\]\\:stroke-border .recharts-curve.recharts-tooltip-cursor {\n  stroke: hsl(var(--border));\n}\r\n.\\[\\&_\\.recharts-dot\\[stroke\\=\\'\\#fff\\'\\]\\]\\:stroke-transparent .recharts-dot[stroke='#fff'] {\n  stroke: transparent;\n}\r\n.\\[\\&_\\.recharts-layer\\]\\:outline-none .recharts-layer {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\r\n.\\[\\&_\\.recharts-polar-grid_\\[stroke\\=\\'\\#ccc\\'\\]\\]\\:stroke-border .recharts-polar-grid [stroke='#ccc'] {\n  stroke: hsl(var(--border));\n}\r\n.\\[\\&_\\.recharts-radial-bar-background-sector\\]\\:fill-muted .recharts-radial-bar-background-sector {\n  fill: hsl(var(--muted));\n}\r\n.\\[\\&_\\.recharts-rectangle\\.recharts-tooltip-cursor\\]\\:fill-muted .recharts-rectangle.recharts-tooltip-cursor {\n  fill: hsl(var(--muted));\n}\r\n.\\[\\&_\\.recharts-reference-line_\\[stroke\\=\\'\\#ccc\\'\\]\\]\\:stroke-border .recharts-reference-line [stroke='#ccc'] {\n  stroke: hsl(var(--border));\n}\r\n.\\[\\&_\\.recharts-sector\\[stroke\\=\\'\\#fff\\'\\]\\]\\:stroke-transparent .recharts-sector[stroke='#fff'] {\n  stroke: transparent;\n}\r\n.\\[\\&_\\.recharts-sector\\]\\:outline-none .recharts-sector {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\r\n.\\[\\&_\\.recharts-surface\\]\\:outline-none .recharts-surface {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\r\n.\\[\\&_p\\]\\:leading-relaxed p {\n  line-height: 1.625;\n}\r\n.\\[\\&_svg\\]\\:pointer-events-none svg {\n  pointer-events: none;\n}\r\n.\\[\\&_svg\\]\\:size-4 svg {\n  width: 1rem;\n  height: 1rem;\n}\r\n.\\[\\&_svg\\]\\:shrink-0 svg {\n  flex-shrink: 0;\n}\r\n.\\[\\&_tr\\:last-child\\]\\:border-0 tr:last-child {\n  border-width: 0px;\n}\r\n.\\[\\&_tr\\]\\:border-b tr {\n  border-bottom-width: 1px;\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiHA;;;;;AASA;;;;AAeA;;;;;;;;;;;AAiBA;;;;;AAWA;;;;;;AAUA;;;;AAQA;;;;;AAcA;;;;;AASA;;;;AAYA;;;;;;;AAcA;;;;AAQA;;;;;;;AAQA;;;;AAIA;;;;AAUA;;;;;;AAYA;;;;;;;;;;;;;AAqBA;;;;AAUA;;;;;;AAaA;;;;AAQA;;;;AAQA;;;;AAQA;;;;AAUA;;;;;AASA;;;;AASA;;;;;AASA;;;;AAQA;;;;AAgBA;;;;;AAKA;;;;AAIA;;;;;;AAWA;;;;AAQA;;;;AASA;;;;;AAUA;;;;AAQA;;;;AAUA;;;;;AAgBA;;;;;AAOA;;;;AAGE;;;;;;;;;;;;;;;;;;;;;;;AAsBA;;;;;;;;;;;;;;;;;;;;;;AAqBA;;;;AAGA;;;;;AAIF;;;;;;;;AAOA;EAEE;;;;;AAIF;;;;;;;;;;;;AAWA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;;AAMA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAMA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;;AAMA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;AAGA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;;AAKA;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;;;;AASA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAQA;;;;;AAIA;;;;;AAIA;;;;;;;;;;AAUA;;;;AAGA;;;;;;;;;;AAUA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;;;;;AASA;;;;;;;;;;AAkBA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAMA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;EAEE;;;;;AAIF;;;;AAGA;EAEE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;;;EAMA;;;;;;EAMA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;AAIF;EAEE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;;AAKF;EAEE;;;;EAIA;;;;;AAIF;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;;;AAMA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAMA;;;;AAGA;;;;AAGA;;;;;AAQA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA", "debugId": null}}]}