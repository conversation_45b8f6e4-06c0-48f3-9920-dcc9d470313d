import { AdminSidebar } from "@/components/admin/AdminSidebar";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { Rocket } from "lucide-react";

export default function AdminPage() {
  return (
    <div className="min-h-screen bg-muted/40">
      <header className="bg-background border-b sticky top-0 z-10">
        <div className="container flex h-16 items-center justify-between">
          <Link href="/admin" className="flex items-center gap-2">
            <Rocket className="h-6 w-6 text-primary" />
            <h1 className="text-xl font-bold font-headline text-foreground">
              Admin Dashboard
            </h1>
          </Link>
          <Button variant="outline" asChild>
            <Link href="/">View Site</Link>
          </Button>
        </div>
      </header>
      <div className="flex">
        <AdminSidebar />
        <main className="flex-1 p-8">
          <div className="text-center">
            <h2 className="text-2xl font-bold font-headline">Welcome to your Dashboard</h2>
            <p className="text-muted-foreground mt-2">Select an option from the sidebar to get started.</p>
          </div>
        </main>
      </div>
    </div>
  );
}
