import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON> } from "lucide-react";

export function Header() {
  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-14 items-center">
        <div className="mr-6 flex items-center">
          <Link href="/" className="flex items-center space-x-2">
            <Rocket className="h-6 w-6 text-primary" />
            <span className="font-bold font-headline text-lg">ArtechAI</span>
          </Link>
        </div>
        <nav className="hidden md:flex items-center space-x-6 text-sm font-medium">
          <Link
            href="/"
            className="transition-colors hover:text-primary"
          >
            Home
          </Link>
          <Link
            href="#about"
            className="transition-colors hover:text-primary"
          >
            About
          </Link>
          <Link
            href="#categories"
            className="transition-colors hover:text-primary"
          >
            Categories
          </Link>
          <Link
            href="#featured"
            className="transition-colors hover:text-primary"
          >
            Featured
          </Link>
        </nav>
        <div className="flex flex-1 items-center justify-end space-x-4">
          <Button asChild>
            <Link href="/login">Admin Login</Link>
          </Button>
        </div>
      </div>
    </header>
  );
}
