import { Button } from "@/components/ui/button";
import Image from "next/image";
import Link from "next/link";

export function Hero() {
  return (
    <section className="relative w-full h-[60vh] flex items-center justify-center text-center">
      <Image
        src="https://picsum.photos/1200/800"
        alt="Abstract AI background"
        fill
        className="object-cover"
        data-ai-hint="abstract ai"
        priority
      />
      <div className="absolute inset-0 bg-black/60" />
      <div className="relative z-10 max-w-4xl mx-auto px-4 text-white">
        <h1 className="text-4xl md:text-6xl font-bold font-headline leading-tight">
          Artechway: Where Art & Tech Innovation Collide
        </h1>
        <p className="mt-4 text-lg md:text-xl text-neutral-200">
          Your AI-Powered Blog for Art and Tech Innovation. Discover the future,
          today.
        </p>
        <div className="mt-8">
          <Button size="lg" asChild>
            <Link href="#featured">Explore Posts</Link>
          </Button>
        </div>
      </div>
    </section>
  );
}
