'use server';
/**
 * @fileOverview AI-powered SEO blog post generator.
 *
 * - generateBlogPost - A function that generates a blog post from a title.
 * - GenerateBlogPostInput - The input type for the generateBlogPost function.
 * - GenerateBlogPostOutput - The return type for the generateBlogPost function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const GenerateBlogPostInputSchema = z.object({
  title: z
    .string()
    .describe('The title of the blog post to be generated.'),
});
export type GenerateBlogPostInput = z.infer<typeof GenerateBlogPostInputSchema>;

const GenerateBlogPostOutputSchema = z.object({
  content: z.string().describe('The generated SEO-optimized blog post content, formatted in markdown.'),
  tags: z.string().describe('A comma-separated list of relevant SEO keywords/tags for the blog post.'),
});
export type GenerateBlogPostOutput = z.infer<typeof GenerateBlogPostOutputSchema>;

export async function generateBlogPost(input: GenerateBlogPostInput): Promise<GenerateBlogPostOutput> {
  return generateBlogPostFlow(input);
}

const prompt = ai.definePrompt({
  name: 'generateBlogPostPrompt',
  input: {schema: GenerateBlogPostInputSchema},
  output: {schema: GenerateBlogPostOutputSchema},
  prompt: `You are an expert SEO copywriter and content strategist. Your task is to write a high-quality, engaging, and SEO-optimized blog post based on the provided title.

The blog post should be well-structured, easy to read, and provide real value to the reader. Use markdown for formatting (e.g., headings, lists, bold text). Do not use any HTML tags.

Also, provide a comma-separated list of relevant tags for the blog post to improve its searchability.

Blog Post Title: {{{title}}}`,
});

const generateBlogPostFlow = ai.defineFlow(
  {
    name: 'generateBlogPostFlow',
    inputSchema: GenerateBlogPostInputSchema,
    outputSchema: GenerateBlogPostOutputSchema,
  },
  async input => {
    const {output} = await prompt(input);
    return output!;
  }
);
