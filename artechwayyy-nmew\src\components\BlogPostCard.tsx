import Image from "next/image";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";
import Link from "next/link";

interface BlogPostCardProps {
  imageUrl: string;
  title: string;
  summary: string;
  imageHint: string;
  slug: string;
}

export function BlogPostCard({
  imageUrl,
  title,
  summary,
  imageHint,
  slug,
}: BlogPostCardProps) {
  return (
    <Card className="overflow-hidden transition-all duration-300 hover:shadow-xl hover:-translate-y-1 flex flex-col">
      <CardHeader className="p-0">
        <div className="aspect-video relative">
          <Image
            src={imageUrl}
            alt={title}
            width={600}
            height={400}
            className="object-cover w-full h-full"
            data-ai-hint={imageHint}
          />
        </div>
      </CardHeader>
      <CardContent className="pt-6 flex-grow">
        <CardTitle className="text-xl font-headline mb-2">{title}</CardTitle>
        <p className="text-muted-foreground line-clamp-3">{summary}</p>
      </CardContent>
      <CardFooter>
        <Button variant="link" asChild className="text-primary hover:text-accent p-0 font-bold">
          <Link href={`/blog/${slug}`}>
            Read More <ArrowRight className="ml-2 h-4 w-4" />
          </Link>
        </Button>
      </CardFooter>
    </Card>
  );
}
