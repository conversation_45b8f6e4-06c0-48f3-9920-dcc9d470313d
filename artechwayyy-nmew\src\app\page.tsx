import { Header } from "@/components/Header";
import { Hero } from "@/components/Hero";
import { CategoryCard } from "@/components/CategoryCard";
import { BlogPostCard } from "@/components/BlogPostCard";
import { NewsletterSignup } from "@/components/NewsletterSignup";
import { About } from "@/components/About";
import { Footer } from "@/components/Footer";
import {
  Cpu,
  Palette,
  Briefcase,
  Megaphone,
  BrainCircuit,
} from "lucide-react";
import { posts } from "@/lib/posts";

const categories = [
  { title: "AI News", icon: Cpu },
  { title: "AI Design", icon: Palette },
  { title: "AI for Business", icon: Briefcase },
  { title: "AI Marketing", icon: Megaphone },
  { title: "Future of AI", icon: BrainCircuit },
];

const featuredPosts = posts.slice(0, 3);

export default function Home() {
  return (
    <div className="flex flex-col min-h-screen">
      <Header />
      <main className="flex-1">
        <Hero />

        <section id="categories" className="py-16 md:py-24">
          <div className="container">
            <h2 className="text-3xl font-bold text-center font-headline tracking-tight sm:text-4xl">
              Explore Categories
            </h2>
            <div className="mt-12 grid gap-8 md:grid-cols-3 lg:grid-cols-5">
              {categories.map((category) => (
                <CategoryCard
                  key={category.title}
                  title={category.title}
                  icon={category.icon}
                />
              ))}
            </div>
          </div>
        </section>

        <section id="featured" className="py-16 md:py-24 bg-muted/50">
          <div className="container">
            <h2 className="text-3xl font-bold text-center font-headline tracking-tight sm:text-4xl">
              Featured Posts
            </h2>
            <div className="mt-12 grid gap-8 md:grid-cols-2 lg:grid-cols-3">
              {featuredPosts.map((post) => (
                <BlogPostCard key={post.slug} {...post} />
              ))}
            </div>
          </div>
        </section>

        <About />

        <NewsletterSignup />
      </main>
      <Footer />
    </div>
  );
}
